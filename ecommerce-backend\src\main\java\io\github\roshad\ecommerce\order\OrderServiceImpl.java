package io.github.roshad.ecommerce.order;

import io.github.roshad.ecommerce.shipping.ShippingInfo;
import io.github.roshad.ecommerce.shipping.ShippingMapper;
import io.github.roshad.ecommerce.shipping.ShippingRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
@RequiredArgsConstructor
public class OrderServiceImpl implements OrderService {
    private static final Logger logger = LoggerFactory.getLogger(OrderServiceImpl.class);
    private final OrderMapper orderMapper;
    private final OrderItemMapper orderItemMapper;
    private final OrderStatusService orderStatusService;
    private final io.github.roshad.ecommerce.auth.UserService userService;
    private final ShippingMapper shippingMapper;

    @Override
    @Transactional
    public Order createOrder(Order order) {
        // 设置默认状态为PENDING
        OrderStatus pendingStatus = orderStatusService.findByStatusName(OrderStatus.PENDING);
        order.setStatus(pendingStatus);
        order.setStatusId(pendingStatus.getId());
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());

        // 保存订单主表
        orderMapper.insert(order);

        // 调试日志：检查订单项
        System.out.println("=== 订单创建调试信息 ===");
        System.out.println("订单ID: " + order.getId());
        System.out.println("订单项数量: " + (order.getOrderItems() != null ? order.getOrderItems().size() : "null"));
        if (order.getOrderItems() != null) {
            for (int i = 0; i < order.getOrderItems().size(); i++) {
                OrderItem item = order.getOrderItems().get(i);
                System.out.println("订单项 " + (i+1) + ": productId=" + item.getProductId() +
                                 ", quantity=" + item.getQuantity() +
                                 ", price=" + item.getPriceAtPurchase() +
                                 ", name=" + item.getProductNameAtPurchase());
            }
        }

        // 保存订单项
        if (order.getOrderItems() != null && !order.getOrderItems().isEmpty()) {
            System.out.println("开始保存 " + order.getOrderItems().size() + " 个订单项...");
            for (OrderItem item : order.getOrderItems()) {
                item.setOrderId(order.getId());
                System.out.println("保存订单项: " + item.getProductId() + " x " + item.getQuantity());
                orderItemMapper.insert(item);
                System.out.println("订单项保存成功，ID: " + item.getId());
            }
            System.out.println("所有订单项保存完成");
        } else {
            System.out.println("警告：没有订单项需要保存！");
        }
        System.out.println("=== 订单创建完成 ===");

        return order;
    }

    @Override
    public Order getOrderById(Long id) {
        return orderMapper.findById(id);
    }

    @Override
    public List<Order> getOrdersByUserId(Long userId) {
        return orderMapper.findByUserId(userId);
    }

    @Override
    public void updateOrderStatus(Long orderId, String statusName) {
        logger.info("=== OrderService: 开始更新订单状态 ===");
        logger.info("订单ID: {}", orderId);
        logger.info("目标状态: {}", statusName);

        Order order = orderMapper.findById(orderId);
        logger.info("查询到的订单: {}", order);

        if (order != null) {
            OrderStatus newStatus = orderStatusService.findByStatusName(statusName);
            logger.info("查询到的状态对象: {}", newStatus);

            if (newStatus != null) {
                logger.info("更新前订单状态: {}", order.getStatus());
                order.setStatus(newStatus);
                order.setStatusId(newStatus.getId());
                order.setUpdatedAt(LocalDateTime.now());

                logger.info("准备更新订单到数据库...");
                orderMapper.update(order);
                logger.info("=== OrderService: 订单状态更新成功 ===");
            } else {
                logger.error("=== OrderService: 找不到状态 {} ===", statusName);
                throw new IllegalArgumentException("无效的订单状态: " + statusName);
            }
        } else {
            logger.error("=== OrderService: 找不到订单 {} ===", orderId);
            throw new IllegalArgumentException("订单不存在: " + orderId);
        }
    }

    @Override
    public void cancelOrder(Long orderId) {
        updateOrderStatus(orderId, OrderStatus.CANCELLED);
    }

    @Override
    @Transactional
    public void deleteOrder(Long orderId) {
        // 检查订单是否存在
        Order order = orderMapper.findById(orderId);
        if (order == null) {
            throw new IllegalArgumentException("订单不存在");
        }

        // 删除订单项（由于外键约束设置了CASCADE，订单项会自动删除，但为了明确性还是手动删除）
        orderItemMapper.deleteByOrderId(orderId);

        // 删除订单主表
        orderMapper.deleteById(orderId);
    }
    @Override
    public boolean isOrderOwner(Long orderId, String username) {
        Order order = orderMapper.findById(orderId);
        if (order == null) {
            return false;
        }
        Long userId = userService.getUserId(username);
        return order.getUserId().equals(userId);
    }

    @Override
    public List<Order> getAllOrders() {
        return orderMapper.findAll();
    }

    // 发货相关方法实现
    @Override
    @Transactional
    public ShippingInfo shipOrder(Long orderId, ShippingRequest shippingRequest) {
        // 检查订单是否可以发货
        if (!canShipOrder(orderId)) {
            throw new IllegalStateException("订单状态不允许发货");
        }

        // 检查是否已经有发货信息
        ShippingInfo existingShipping = shippingMapper.findByOrderId(orderId);
        if (existingShipping != null) {
            throw new IllegalStateException("该订单已经发货");
        }

        // 创建发货信息
        ShippingInfo shippingInfo = new ShippingInfo();
        shippingInfo.setOrderId(orderId);
        shippingInfo.setTrackingNumber(shippingRequest.getTrackingNumber());
        shippingInfo.setCarrier(shippingRequest.getCarrier());
        shippingInfo.setShippingMethod(shippingRequest.getShippingMethod());
        shippingInfo.setShippedAt(LocalDateTime.now());
        shippingInfo.setNotes(shippingRequest.getNotes());
        shippingInfo.setCreatedAt(LocalDateTime.now());
        shippingInfo.setUpdatedAt(LocalDateTime.now());

        // 计算预计送达时间
        if (shippingRequest.getEstimatedDays() != null && shippingRequest.getEstimatedDays() > 0) {
            shippingInfo.setEstimatedDelivery(
                LocalDateTime.now().plusDays(shippingRequest.getEstimatedDays())
            );
        }

        // 保存发货信息
        shippingMapper.insert(shippingInfo);

        // 更新订单状态为已发货
        updateOrderStatus(orderId, OrderStatus.SHIPPED);

        return shippingInfo;
    }

    @Override
    public ShippingInfo getOrderShipping(Long orderId) {
        return shippingMapper.findByOrderId(orderId);
    }

    @Override
    @Transactional
    public ShippingInfo updateOrderShipping(Long orderId, ShippingRequest shippingRequest) {
        ShippingInfo shippingInfo = shippingMapper.findByOrderId(orderId);
        if (shippingInfo == null) {
            throw new IllegalArgumentException("该订单尚未发货");
        }

        // 更新发货信息
        shippingInfo.setTrackingNumber(shippingRequest.getTrackingNumber());
        shippingInfo.setCarrier(shippingRequest.getCarrier());
        shippingInfo.setShippingMethod(shippingRequest.getShippingMethod());
        shippingInfo.setNotes(shippingRequest.getNotes());
        shippingInfo.setUpdatedAt(LocalDateTime.now());

        // 重新计算预计送达时间
        if (shippingRequest.getEstimatedDays() != null && shippingRequest.getEstimatedDays() > 0) {
            shippingInfo.setEstimatedDelivery(
                shippingInfo.getShippedAt().plusDays(shippingRequest.getEstimatedDays())
            );
        }

        shippingMapper.update(shippingInfo);
        return shippingInfo;
    }

    @Override
    public List<Order> getOrdersReadyToShip() {
        List<Order> allOrders = orderMapper.findAll();
        return allOrders.stream()
            .filter(order -> canShipOrder(order.getId()))
            .collect(Collectors.toList());
    }

    @Override
    public boolean canShipOrder(Long orderId) {
        Order order = getOrderById(orderId);
        if (order == null) {
            return false;
        }

        // 检查是否已经发货
        ShippingInfo existingShipping = shippingMapper.findByOrderId(orderId);
        if (existingShipping != null) {
            return false; // 已经发货的订单不能再次发货
        }

        // 只有已付款或处理中的订单可以发货
        String statusName = order.getStatus().getStatusName();
        return OrderStatus.PAID.equals(statusName) || OrderStatus.PROCESSING.equals(statusName);
    }
}