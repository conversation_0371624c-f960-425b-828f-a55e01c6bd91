import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Order, OrderStatus, CreateOrderRequest, ShippingInfo, ShippingRequest } from '../types/order';
import { orderApi, getCurrentUserIdSafe } from '../utils/api';
import { useAddressStore } from './addressStore';

// 订单管理状态接口
interface OrderState {
  orders: Order[];
  currentOrder: Order | null;
  isLoading: boolean;
  error: string | null;

  // 发货信息缓存
  shippingInfos: Map<number, ShippingInfo>; // orderId -> ShippingInfo

  // 操作方法
  createOrder: (orderData: CreateOrderRequest) => Promise<Order>;
  getOrderById: (id: number) => Order | null;
  getUserOrders: (userId?: number) => Order[];
  loadOrdersFromBackend: (userId?: number) => Promise<void>;
  syncWithBackend: (userId?: number) => Promise<void>;
  updateOrderStatus: (orderId: number, status: OrderStatus) => void;
  cancelOrder: (orderId: number) => void;
  setCurrentOrder: (order: Order | null) => void;
  clearOrders: () => void;

  // 发货相关方法
  shipOrder: (orderId: number, shippingData: ShippingRequest) => Promise<ShippingInfo>;
  getOrderShipping: (orderId: number) => Promise<ShippingInfo | null>;
  updateOrderShipping: (orderId: number, shippingData: ShippingRequest) => Promise<ShippingInfo>;
  getOrdersReadyToShip: () => Order[];

  // 统计方法
  getOrdersByStatus: (status: OrderStatus) => Order[];
  getTotalSpent: () => number;
}

// 这些函数已移到 api.ts 中，这里不再需要

// 创建订单管理 store
export const useOrderStore = create<OrderState>()(
  persist(
    (set, get) => ({
      orders: [], // 初始为空，将从后端加载
      currentOrder: null,
      isLoading: false,
      error: null,
      shippingInfos: new Map(),

      // 创建订单
      createOrder: async (orderData: CreateOrderRequest) => {
        set({ isLoading: true, error: null });

        try {
          // 获取选中的收货地址
          const addressStore = useAddressStore.getState();
          const shippingAddress = addressStore.addresses.find(addr => addr.id === orderData.shippingAddressId);

          if (!shippingAddress) {
            throw new Error('请选择收货地址');
          }

          // 调用后端API创建订单
          const backendOrder = await orderApi.create(orderData, shippingAddress) as any;

          console.log('后端返回的订单数据:', backendOrder);

          // 转换后端返回的订单数据为前端格式
          const orderItems = backendOrder.orderItems?.map((item: any) => {
            const price = item.priceAtPurchase || item.price || 0;
            const quantity = item.quantity || 1;
            return {
              id: item.id,
              orderId: item.orderId,
              productId: item.productId,
              productName: item.productNameAtPurchase || item.productName || `商品 ${item.productId}`,
              productImage: '/placeholder.jpg', // 需要从商品信息获取
              price: price,
              quantity: quantity,
              subtotal: price * quantity
            };
          }) || [];

          console.log('转换后的订单项:', orderItems);

          const newOrder: Order = {
            id: backendOrder.id,
            userId: backendOrder.userId,
            orderNumber: backendOrder.orderCode,
            status: backendOrder.status?.statusName as OrderStatus || OrderStatus.PENDING,
            items: orderItems,
            subtotal: backendOrder.totalAmount - 10, // 简化计算，实际应该从后端获取详细信息
            shippingFee: 10, // 简化处理
            discount: 0,
            totalAmount: backendOrder.totalAmount,
            shippingAddress,
            paymentMethod: orderData.paymentMethod,
            paymentStatus: backendOrder.paymentStatus as 'PENDING' | 'COMPLETED' | 'FAILED',
            createdAt: backendOrder.createdAt || new Date().toISOString(),
            updatedAt: backendOrder.updatedAt || new Date().toISOString(),
            notes: backendOrder.notes
          };

          // 将新订单添加到现有订单列表中
          const { orders } = get();
          set({
            orders: [newOrder, ...orders],
            currentOrder: newOrder,
            isLoading: false
          });

          return newOrder;
        } catch (error) {
          console.error('创建订单失败:', error);
          set({
            error: error instanceof Error ? error.message : '创建订单失败',
            isLoading: false
          });
          throw error;
        }
      },

      // 根据ID获取订单
      getOrderById: (id: number) => {
        const { orders } = get();
        return orders.find(order => order.id === id) || null;
      },

      // 获取用户订单
      getUserOrders: (_userId?: number) => {
        const { orders } = get();
        // 返回當前加載的订单（已經是當前用戶的订单）
        return orders;
      },

      // 从后端加载订单（替换所有本地数据）
      loadOrdersFromBackend: async (userId?: number) => {
        set({ isLoading: true, error: null });

        try {
          // 调用后端API获取用户订单
          const backendOrders = await orderApi.getByUserId(userId) as any[];

          console.log('从后端获取的订单数据:', backendOrders);

          // 转换后端订单数据为前端格式
          const orders: Order[] = backendOrders.map((backendOrder: any) => {
            console.log('处理单个后端订单:', backendOrder);
            console.log('订单的orderItems字段:', backendOrder.orderItems);

            const orderItems = backendOrder.orderItems?.map((item: any) => {
              console.log('处理订单项:', item);
              const price = item.priceAtPurchase || item.price || 0;
              const quantity = item.quantity || 1;
              return {
                id: item.id,
                orderId: item.orderId,
                productId: item.productId,
                productName: item.productNameAtPurchase || item.productName || `商品 ${item.productId}`,
                productImage: '/placeholder.jpg',
                price: price,
                quantity: quantity,
                subtotal: price * quantity
              };
            }) || [];

            console.log('转换后的订单项:', orderItems);

            if (orderItems.length === 0) {
              console.warn('警告：订单项为空！原始数据:', backendOrder.orderItems);
            }

            return {
              id: backendOrder.id,
              userId: backendOrder.userId,
              orderNumber: backendOrder.orderCode,
              status: backendOrder.status?.statusName as OrderStatus || OrderStatus.PENDING,
              items: orderItems,
              subtotal: backendOrder.totalAmount - 10, // 简化计算
              shippingFee: 10,
              discount: 0,
              totalAmount: backendOrder.totalAmount,
              shippingAddress: {
                id: 1,
                recipientName: '收件人',
                phone: '13800138000',
                province: '广东省',
                city: '深圳市',
                district: '南山區',
                detailAddress: backendOrder.shippingAddressLine1 || '详细地址',
                isDefault: false
              }, // 需要解析后端地址信息
              paymentMethod: backendOrder.paymentMethod,
              paymentStatus: backendOrder.paymentStatus as 'PENDING' | 'COMPLETED' | 'FAILED',
              createdAt: backendOrder.createdAt || new Date().toISOString(),
              updatedAt: backendOrder.updatedAt || new Date().toISOString(),
              notes: backendOrder.notes
            };
          });

          // 完全替换本地订单数据，以后端为准
          set({
            orders,
            isLoading: false
          });

          // 清除可能存在的本地模拟数据
          if (typeof window !== 'undefined') {
            const currentStorage = localStorage.getItem('order-storage');
            if (currentStorage) {
              try {
                const parsed = JSON.parse(currentStorage);
                // 只保留后端数据，清除本地模拟数据
                const cleanedStorage = {
                  ...parsed,
                  state: {
                    ...parsed.state,
                    orders // 使用后端数据覆盖
                  }
                };
                localStorage.setItem('order-storage', JSON.stringify(cleanedStorage));
              } catch (e) {
                console.error('清理本地存储失败:', e);
              }
            }
          }
        } catch (error) {
          console.error('加载订单失败:', error);
          set({
            error: error instanceof Error ? error.message : '加载订单失败',
            isLoading: false
          });
        }
      },

      // 强制同步后端数据（清除本地缓存）
      syncWithBackend: async (userId?: number) => {
        // 清除本地存储中的订单数据
        if (typeof window !== 'undefined') {
          const currentStorage = localStorage.getItem('order-storage');
          if (currentStorage) {
            try {
              const parsed = JSON.parse(currentStorage);
              const cleanedStorage = {
                ...parsed,
                state: {
                  ...parsed.state,
                  orders: [] // 清空本地订单
                }
              };
              localStorage.setItem('order-storage', JSON.stringify(cleanedStorage));
            } catch (e) {
              console.error('清理本地存储失败:', e);
            }
          }
        }

        // 重新从后端加载
        await get().loadOrdersFromBackend(userId);
      },

      // 更新订单状态
      updateOrderStatus: (orderId: number, status: OrderStatus) => {
        const { orders } = get();
        const updatedOrders = orders.map(order => {
          if (order.id === orderId) {
            const updatedOrder = {
              ...order,
              status,
              updatedAt: new Date().toISOString()
            };
            
            // 如果状态变为已付款，更新支付状态和时间
            if (status === OrderStatus.PAID) {
              updatedOrder.paymentStatus = 'COMPLETED';
              updatedOrder.paidAt = new Date().toISOString();
            }
            
            return updatedOrder;
          }
          return order;
        });

        set({ orders: updatedOrders });
      },

      // 取消订单
      cancelOrder: (orderId: number) => {
        get().updateOrderStatus(orderId, OrderStatus.CANCELLED);
      },

      // 设置当前订单
      setCurrentOrder: (order: Order | null) => {
        set({ currentOrder: order });
      },

      // 清空订单
      clearOrders: () => {
        set({
          orders: [],
          currentOrder: null,
          error: null
        });
      },

      // 根据状态获取订单
      getOrdersByStatus: (status: OrderStatus) => {
        const { orders } = get();
        return orders.filter(order => order.status === status);
      },

      // 计算总消费
      getTotalSpent: () => {
        const { orders } = get();
        return orders
          .filter(order => order.status !== OrderStatus.CANCELLED)
          .reduce((total, order) => total + order.totalAmount, 0);
      },

      // 发货相关方法
      shipOrder: async (orderId: number, shippingData: ShippingRequest) => {
        set({ isLoading: true, error: null });

        try {
          // 模拟API调用
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 實際應該調用後端API
          // const response = await fetch(`/api/orders/${orderId}/ship`, {
          //   method: 'POST',
          //   headers: { 'Content-Type': 'application/json' },
          //   body: JSON.stringify(shippingData)
          // });
          // const shippingInfo = await response.json();

          // 模拟创建发货信息
          const now = new Date().toISOString();
          const shippingInfo: ShippingInfo = {
            id: Date.now(),
            orderId,
            trackingNumber: shippingData.trackingNumber,
            carrier: shippingData.carrier,
            shippingMethod: shippingData.shippingMethod,
            shippedAt: now,
            estimatedDelivery: shippingData.estimatedDays
              ? new Date(Date.now() + shippingData.estimatedDays * 24 * 60 * 60 * 1000).toISOString()
              : undefined,
            notes: shippingData.notes,
            createdAt: now,
            updatedAt: now
          };

          // 更新发货信息缓存
          const { shippingInfos } = get();
          const newShippingInfos = new Map(shippingInfos);
          newShippingInfos.set(orderId, shippingInfo);

          // 更新订单状态為已發貨
          get().updateOrderStatus(orderId, OrderStatus.SHIPPED);

          set({
            shippingInfos: newShippingInfos,
            isLoading: false
          });

          return shippingInfo;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : '发货失败',
            isLoading: false
          });
          throw error;
        }
      },

      getOrderShipping: async (orderId: number) => {
        const { shippingInfos } = get();

        // 先检查缓存
        const cachedShipping = shippingInfos.get(orderId);
        if (cachedShipping) {
          return cachedShipping;
        }

        try {
          // 實際應該調用後端API
          // const response = await fetch(`/api/orders/${orderId}/shipping`);
          // if (response.ok) {
          //   const shippingInfo = await response.json();
          //   // 更新緩存
          //   const newShippingInfos = new Map(shippingInfos);
          //   newShippingInfos.set(orderId, shippingInfo);
          //   set({ shippingInfos: newShippingInfos });
          //   return shippingInfo;
          // }

          return null;
        } catch (error) {
          console.error('获取发货信息失败:', error);
          return null;
        }
      },

      updateOrderShipping: async (orderId: number, shippingData: ShippingRequest) => {
        set({ isLoading: true, error: null });

        try {
          // 模拟API调用
          await new Promise(resolve => setTimeout(resolve, 1000));

          // 實際應該調用後端API
          // const response = await fetch(`/api/orders/${orderId}/shipping`, {
          //   method: 'PUT',
          //   headers: { 'Content-Type': 'application/json' },
          //   body: JSON.stringify(shippingData)
          // });
          // const shippingInfo = await response.json();

          // 模擬更新發貨信息
          const { shippingInfos } = get();
          const existingShipping = shippingInfos.get(orderId);

          if (!existingShipping) {
            throw new Error('该订单尚未发货');
          }

          const updatedShipping: ShippingInfo = {
            ...existingShipping,
            trackingNumber: shippingData.trackingNumber,
            carrier: shippingData.carrier,
            shippingMethod: shippingData.shippingMethod,
            notes: shippingData.notes,
            estimatedDelivery: shippingData.estimatedDays
              ? new Date(new Date(existingShipping.shippedAt).getTime() + shippingData.estimatedDays * 24 * 60 * 60 * 1000).toISOString()
              : existingShipping.estimatedDelivery,
            updatedAt: new Date().toISOString()
          };

          // 更新緩存
          const newShippingInfos = new Map(shippingInfos);
          newShippingInfos.set(orderId, updatedShipping);

          set({
            shippingInfos: newShippingInfos,
            isLoading: false
          });

          return updatedShipping;
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : '更新发货信息失败',
            isLoading: false
          });
          throw error;
        }
      },

      getOrdersReadyToShip: () => {
        const { orders } = get();
        return orders.filter(order =>
          order.status === OrderStatus.PAID || order.status === OrderStatus.PROCESSING
        );
      }
    }),
    {
      name: 'order-storage',
      version: 2, // 增加版本号以清除旧数据
      partialize: (state) => ({
        // 只持久化必要的状态，不持久化订单列表
        currentOrder: state.currentOrder,
        shippingInfos: state.shippingInfos,
        // 不持久化 orders 数组，每次都从后端加载
      }),
      migrate: (persistedState: any, version: number) => {
        // 迁移函数：清除旧版本的订单数据
        if (version < 2) {
          return {
            ...persistedState,
            orders: [], // 清空旧的订单数据
          };
        }
        return persistedState;
      },
    }
  )
);

// 初始化函数：确保订单数据从后端加载
export const initializeOrderStore = async () => {
  try {
    // 检查用户是否已登录
    const userId = getCurrentUserIdSafe();
    if (!userId) {
      console.log('用户未登录，跳过订单数据初始化');
      return;
    }

    // 检查是否已经有订单数据，如果没有则从后端加载
    const state = useOrderStore.getState();
    if (state.orders.length === 0 && !state.isLoading) {
      console.log('开始初始化订单数据，用户ID:', userId);
      await state.loadOrdersFromBackend(userId);
    }
  } catch (error) {
    console.error('初始化订单存储失败:', error);
  }
};
