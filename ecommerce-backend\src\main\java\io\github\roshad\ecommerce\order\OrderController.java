package io.github.roshad.ecommerce.order; // Test for hot r1

import io.github.roshad.ecommerce.shipping.ShippingInfo;
import io.github.roshad.ecommerce.shipping.ShippingRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/orders")
@RequiredArgsConstructor
public class OrderController {
    private final OrderService orderService;

    @PostMapping
    public ResponseEntity<Order> createOrder(@RequestBody Order order, 
                                            @RequestHeader("Authorization") String token) {
        // 从token解析用户ID并验证
        Order createdOrder = orderService.createOrder(order);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdOrder);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Order> getOrderById(@PathVariable Long id, 
                                             @RequestHeader("Authorization") String token) {
        // 验证用户是否有权访问该订单
        Order order = orderService.getOrderById(id);
        return ResponseEntity.ok(order);
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<List<Order>> getOrdersByUserId(@PathVariable Long userId, 
                                                        @RequestHeader("Authorization") String token) {
        // 验证token中的用户ID与路径参数一致
        List<Order> orders = orderService.getOrdersByUserId(userId);
        return ResponseEntity.ok(orders);
    }
    
        @GetMapping
        @PreAuthorize("hasRole('ADMIN')")
        public ResponseEntity<List<Order>> getAllOrders() {
            List<Order> orders = orderService.getAllOrders();
            return ResponseEntity.ok(orders);
        }

    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> updateOrderStatus(@PathVariable Long id, 
                                                 @RequestBody OrderStatusUpdateRequest request) {
        orderService.updateOrderStatus(id, request.getStatusName());
        return ResponseEntity.noContent().build();
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteOrder(@PathVariable Long id,
                                           @RequestHeader("Authorization") String token) {
        // 权限验证由SecurityConfig处理：ADMIN可以删除任何订单，用户只能删除自己的订单
        orderService.deleteOrder(id);
        return ResponseEntity.noContent().build();
    }

    @PatchMapping("/{id}/cancel")
    public ResponseEntity<Void> cancelOrder(@PathVariable Long id,
                                           @RequestHeader("Authorization") String token) {
        // 取消订单（设置状态为CANCELLED）
        orderService.cancelOrder(id);
        return ResponseEntity.noContent().build();
    }

    // 发货相关端点
    @PostMapping("/{id}/ship")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ShippingInfo> shipOrder(@PathVariable Long id,
                                                 @Valid @RequestBody ShippingRequest shippingRequest) {
        try {
            ShippingInfo shippingInfo = orderService.shipOrder(id, shippingRequest);
            return ResponseEntity.status(HttpStatus.CREATED).body(shippingInfo);
        } catch (IllegalStateException e) {
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/{id}/shipping")
    public ResponseEntity<ShippingInfo> getOrderShipping(@PathVariable Long id,
                                                        @RequestHeader("Authorization") String token) {
        // 验证用户是否有权访问该订单的发货信息
        ShippingInfo shippingInfo = orderService.getOrderShipping(id);
        if (shippingInfo == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(shippingInfo);
    }

    @PutMapping("/{id}/shipping")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ShippingInfo> updateOrderShipping(@PathVariable Long id,
                                                           @Valid @RequestBody ShippingRequest shippingRequest) {
        try {
            ShippingInfo shippingInfo = orderService.updateOrderShipping(id, shippingRequest);
            return ResponseEntity.ok(shippingInfo);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/ready-to-ship")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<List<Order>> getOrdersReadyToShip() {
        List<Order> orders = orderService.getOrdersReadyToShip();
        return ResponseEntity.ok(orders);
    }
}