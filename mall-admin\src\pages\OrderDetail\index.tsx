import React from 'react';
import { PageContainer } from '@ant-design/pro-components';
import { Card, Descriptions, Spin, message, Button, Modal, Form, Input, Select, Space } from 'antd';
import { useParams } from '@umijs/max';
import { useEffect, useState } from 'react';
import API from '@/services/ant-design-pro/api';

const OrderDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [orderDetail, setOrderDetail] = useState<API.Order | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [shippingModalVisible, setShippingModalVisible] = useState<boolean>(false);
  const [shippingLoading, setShippingLoading] = useState<boolean>(false);
  const [form] = Form.useForm();

  useEffect(() => {
    const fetchOrderDetail = async () => {
      try {
        setLoading(true);
        const response = await API.Order.getOrderById({ id: parseInt(id!) });
        setOrderDetail(response);
      } catch (error) {
        message.error('获取订单详情失败');
      } finally {
        setLoading(false);
      }
    };
    if (id) {
      fetchOrderDetail();
    }
  }, [id]);

  // 处理发货
  const handleShipping = async (values: any) => {
    try {
      setShippingLoading(true);
      await API.Order.updateOrderShipping({
        id: parseInt(id!),
        ...values
      });
      message.success('发货成功！');
      setShippingModalVisible(false);
      form.resetFields();
      // 重新获取订单详情
      const response = await API.Order.getOrderById({ id: parseInt(id!) });
      setOrderDetail(response);
    } catch (error) {
      message.error('发货失败，请重试');
    } finally {
      setShippingLoading(false);
    }
  };

  // 更新订单状态
  const updateOrderStatus = async (statusName: string) => {
    try {
      await API.Order.updateOrderStatus({
        id: parseInt(id!),
        statusName
      });
      message.success('状态更新成功！');
      // 重新获取订单详情
      const response = await API.Order.getOrderById({ id: parseInt(id!) });
      setOrderDetail(response);
    } catch (error) {
      message.error('状态更新失败，请重试');
    }
  };

  if (loading) {
    return <Spin tip="加载中..." />;
  }

  if (!orderDetail) {
    return <PageContainer>订单详情加载失败或订单不存在</PageContainer>;
  }

  return (
    <PageContainer header={{ title: `订单详情 - ${orderDetail.id}` }}>
      <Card
        title="基本信息"
        extra={
          <Space>
            {orderDetail.status?.statusName === 'PENDING' && (
              <Button type="primary" onClick={() => updateOrderStatus('PAID')}>
                标记为已付款
              </Button>
            )}
            {(orderDetail.status?.statusName === 'PAID' || orderDetail.status?.statusName === 'PROCESSING') && (
              <Button type="primary" onClick={() => setShippingModalVisible(true)}>
                发货
              </Button>
            )}
            {orderDetail.status?.statusName === 'SHIPPED' && (
              <Button onClick={() => updateOrderStatus('DELIVERED')}>
                标记为已送达
              </Button>
            )}
          </Space>
        }
      >
        <Descriptions column={2}>
          <Descriptions.Item label="订单ID">{orderDetail.id}</Descriptions.Item>
          <Descriptions.Item label="用户名">{orderDetail.userName}</Descriptions.Item>
          <Descriptions.Item label="总价">{orderDetail.totalAmount}</Descriptions.Item>
          <Descriptions.Item label="状态">{orderDetail.status?.statusName}</Descriptions.Item>
          <Descriptions.Item label="创建时间">{orderDetail.createdAt}</Descriptions.Item>
          <Descriptions.Item label="更新时间">{orderDetail.updatedAt}</Descriptions.Item>
        </Descriptions>
      </Card>
      <Card title="商品列表" style={{ marginTop: 16 }}>
        <Descriptions column={1}>
          {orderDetail.orderItems?.map((item, index) => (
            <Descriptions.Item key={index} label={`商品 ${index + 1}`}>
              {item.productNameAtPurchase} - 单价: {item.price} - 数量: {item.quantity}
            </Descriptions.Item>
          ))}
        </Descriptions>
      </Card>
      {/* 可以在这里添加更多详情，如物流信息、支付信息等 */}
    </PageContainer>
  );
};

export default OrderDetail;
