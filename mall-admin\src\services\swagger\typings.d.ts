declare namespace API {
  type getAllOrdersParams = {
    current?: number;
    pageSize?: number;
  };

  type cancelOrderParams = {
    id: number;
  };

  type deleteProductParams = {
    id: number;
  };

  type getOrderByIdParams = {
    id: number;
  };

  type getOrdersByUserIdParams = {
    userId: number;
  };

  type getProductByIdParams = {
    id: number;
  };

  type getUserByUsernameParams = {
    username: string;
  };

  type GrantedAuthority = {
    authority?: string;
  };

  type LoginRequest = {
    username?: string;
    password?: string;
  };

  type Order = {
    id?: number;
    userId?: number;
    userName: string;
    orderCode?: string;
    totalAmount?: number;
    shippingAddressLine1?: string;
    shippingAddressLine2?: string;
    shippingCity?: string;
    shippingPostalCode?: string;
    shippingCountry?: string;
    paymentMethod?: string;
    paymentStatus?: string;
    notes?: string;
    orderItems?: OrderItem[];
    products: Product[]; // 添加商品数组
    status?: OrderStatus;
    createdAt?: string;
    updatedAt?: string;
  };

  type OrderItem = {
    id?: number;
    productId?: number;
    quantity?: number;
    price?: number;
    priceAtPurchase?: number;
    productNameAtPurchase?: string;
    order?: Order;
  };

  type OrderStatus = {
    id?: number;
    statusName?: string;
    description?: string;
    createdAt?: string;
    updatedAt?: string;
  };

  type OrderStatusUpdateRequest = {
    statusName?: string;
  };

  type Product = {
    id?: number;
    name: string;
    description?: string;
    price: number;
    quantity: number; // 添加 quantity 属性
    stock: number;
    categoryId?: number;
    imageUrl?: string;
    tags?: string;
    createdAt?: string;
    updatedAt?: string;
  };

  type searchProductsParams = {
    keyword: string;
  };

  type updateOrderStatusParams = {
    id: number;
  };

  type updateProductParams = {
    id: number;
  };

  type User = {
    id?: number;
    username?: string;
    passwordHash?: string;
    email?: string;
    role?: string;
    createdAt?: string;
    updatedAt?: string;
    enabled?: boolean;
    password?: string;
    authorities?: GrantedAuthority[];
    credentialsNonExpired?: boolean;
    accountNonExpired?: boolean;
    accountNonLocked?: boolean;
  };
}
