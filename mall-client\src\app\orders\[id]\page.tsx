'use client';

import React, { useEffect, useState, use } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowLeftIcon, CheckCircleIcon, ClockIcon, TruckIcon } from '@heroicons/react/24/outline';
import { useOrderStore } from '../../../store/orderStore';
import { useAuthStore } from '../../../store/authStore';
import { Order, OrderStatus, ShippingInfo } from '../../../types/order';
import toast from 'react-hot-toast';

interface OrderDetailPageProps {
  params: {
    id: string;
  };
}

export default function OrderDetailPage({ params }: OrderDetailPageProps) {
  const router = useRouter();
  const { getOrderById, updateOrderStatus, getOrderShipping, syncWithBackend } = useOrderStore();
  const { isAuthenticated, user } = useAuthStore();
  const [order, setOrder] = useState<Order | null>(null);
  const [shippingInfo, setShippingInfo] = useState<ShippingInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 使用React.use()解包params Promise
  const resolvedParams = use(params);
  const orderId = parseInt(resolvedParams.id);

  // 检查认证状态
  useEffect(() => {
    if (!isAuthenticated) {
      toast.error('请先登录查看订单详情');
      router.push('/login');
      return;
    }
  }, [isAuthenticated, router]);

  useEffect(() => {
    if (!isAuthenticated) return; // 如果未认证，不执行后续逻辑

    if (isNaN(orderId)) {
      toast.error('無效的订单ID');
      router.push('/orders');
      return;
    }

    // 首先尝试从本地状态获取订单
    let foundOrder = getOrderById(orderId);

    if (foundOrder) {
      setOrder(foundOrder);

      // 如果订单已发货，获取发货信息
      if (foundOrder.status === OrderStatus.SHIPPED || foundOrder.status === OrderStatus.DELIVERED) {
        getOrderShipping(orderId).then(shipping => {
          setShippingInfo(shipping);
        }).catch(error => {
          console.error('获取发货信息失败:', error);
        });
      }

      setIsLoading(false);
    } else {
      // 如果本地没有，强制从后端重新加载所有订单数据
      const userId = user?.id;
      if (userId) {
        syncWithBackend(userId).then(() => {
          const reloadedOrder = getOrderById(orderId);
          if (reloadedOrder) {
            setOrder(reloadedOrder);
          } else {
            toast.error('订单不存在或已被删除');
            router.push('/orders');
          }
          setIsLoading(false);
        }).catch(error => {
          console.error('加载订单失败:', error);
          toast.error('加载订单失败');
          router.push('/orders');
          setIsLoading(false);
        });
      } else {
        toast.error('用户信息不完整');
        router.push('/orders');
        setIsLoading(false);
      }
    }
  }, [orderId, getOrderById, getOrderShipping, syncWithBackend, router, isAuthenticated, user]);

  const handlePayment = () => {
    if (!order) return;

    // 模擬支付過程
    toast.loading('正在處理支付...', { duration: 2000 });

    setTimeout(() => {
      updateOrderStatus(order.id!, OrderStatus.PAID);
      setOrder(prev => prev ? { ...prev, status: OrderStatus.PAID } : null);
      toast.success('支付成功！');
    }, 2000);
  };

  const handleCancelOrder = () => {
    if (!order) return;

    if (confirm('確定要取消這個订单嗎？')) {
      updateOrderStatus(order.id!, OrderStatus.CANCELLED);
      setOrder(prev => prev ? { ...prev, status: OrderStatus.CANCELLED } : null);
      toast.success('订单已取消');
    }
  };

  // 獲取订单狀態信息
  const getStatusInfo = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PENDING:
        return { text: '待付款', color: 'text-orange-600', bgColor: 'bg-orange-100', icon: ClockIcon };
      case OrderStatus.PAID:
        return { text: '已付款', color: 'text-blue-600', bgColor: 'bg-blue-100', icon: CheckCircleIcon };
      case OrderStatus.PROCESSING:
        return { text: '處理中', color: 'text-blue-600', bgColor: 'bg-blue-100', icon: ClockIcon };
      case OrderStatus.SHIPPED:
        return { text: '已發貨', color: 'text-purple-600', bgColor: 'bg-purple-100', icon: TruckIcon };
      case OrderStatus.DELIVERED:
        return { text: '已送達', color: 'text-green-600', bgColor: 'bg-green-100', icon: CheckCircleIcon };
      case OrderStatus.CANCELLED:
        return { text: '已取消', color: 'text-red-600', bgColor: 'bg-red-100', icon: ClockIcon };
      default:
        return { text: '未知', color: 'text-gray-600', bgColor: 'bg-gray-100', icon: ClockIcon };
    }
  };

  // 處理圖片URL
  const getImageUrl = (url: string, productName: string) => {
    if (!url) return '/placeholder.jpg';

    if (url.startsWith('/images/')) {
      return `https://via.placeholder.com/80x80/f0f0f0/666666?text=${encodeURIComponent(productName)}`;
    }

    try {
      new URL(url);
      return url;
    } catch {
      return `https://via.placeholder.com/80x80/f0f0f0/666666?text=${encodeURIComponent(productName)}`;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加載中...</p>
        </div>
      </div>
    );
  }

  if (!order) {
    return null;
  }

  const statusInfo = getStatusInfo(order.status);
  const StatusIcon = statusInfo.icon;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 頁面標題 */}
      <div className="flex items-center mb-6">
        <Link
          href="/orders"
          className="flex items-center text-gray-600 hover:text-gray-800 mr-4"
        >
          <ArrowLeftIcon className="w-5 h-5 mr-1" />
          返回订单列表
        </Link>
        <h1 className="text-2xl font-bold text-gray-900">订单詳情</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 左側：订单信息 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 订单狀態 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">订单狀態</h2>
              <div className={`flex items-center space-x-2 px-3 py-1 rounded-full ${statusInfo.bgColor}`}>
                <StatusIcon className={`w-4 h-4 ${statusInfo.color}`} />
                <span className={`text-sm font-medium ${statusInfo.color}`}>
                  {statusInfo.text}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">订单號：</span>
                <span className="font-medium">{order.orderNumber}</span>
              </div>
              <div>
                <span className="text-gray-600">下單時間：</span>
                <span className="font-medium">
                  {new Date(order.createdAt).toLocaleString('zh-CN')}
                </span>
              </div>
              {order.paidAt && (
                <div>
                  <span className="text-gray-600">付款時間：</span>
                  <span className="font-medium">
                    {new Date(order.paidAt).toLocaleString('zh-CN')}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* 商品列表 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">商品清單</h2>
            <div className="space-y-4">
              {order.items.map((item) => (
                <div key={item.id} className="flex items-center space-x-4 p-4 border border-gray-100 rounded-lg">
                  <div className="relative w-16 h-16 flex-shrink-0">
                    <Image
                      src={getImageUrl(item.productImage, item.productName)}
                      alt={item.productName}
                      fill
                      style={{ objectFit: "cover" }}
                      className="rounded-md"
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{item.productName}</h3>
                    <p className="text-sm text-gray-600">
                      ¥{item.price.toFixed(2)} × {item.quantity}
                    </p>
                  </div>
                  <div className="text-lg font-semibold text-gray-900">
                    ¥{item.subtotal.toFixed(2)}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 收貨地址 */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">收貨地址</h2>
            <div className="text-sm">
              <p className="font-medium text-gray-900 mb-1">
                {order.shippingAddress.recipientName} {order.shippingAddress.phone}
              </p>
              <p className="text-gray-600">
                {order.shippingAddress.province} {order.shippingAddress.city} {order.shippingAddress.district}
              </p>
              <p className="text-gray-600">
                {order.shippingAddress.detailAddress}
              </p>
              {order.shippingAddress.postalCode && (
                <p className="text-gray-500 mt-1">
                  郵編: {order.shippingAddress.postalCode}
                </p>
              )}
            </div>
          </div>

          {/* 发货信息 */}
          {(order.status === OrderStatus.SHIPPED || order.status === OrderStatus.DELIVERED) && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">發貨信息</h2>
              {shippingInfo ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">運單號：</span>
                      <span className="font-medium text-blue-600">{shippingInfo.trackingNumber}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">快遞公司：</span>
                      <span className="font-medium">{shippingInfo.carrier}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">配送方式：</span>
                      <span className="font-medium">{shippingInfo.shippingMethod}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">發貨時間：</span>
                      <span className="font-medium">
                        {new Date(shippingInfo.shippedAt).toLocaleString('zh-CN')}
                      </span>
                    </div>
                    {shippingInfo.estimatedDelivery && (
                      <div>
                        <span className="text-gray-600">預計送達：</span>
                        <span className="font-medium text-green-600">
                          {new Date(shippingInfo.estimatedDelivery).toLocaleString('zh-CN')}
                        </span>
                      </div>
                    )}
                  </div>

                  {shippingInfo.notes && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-600 text-sm">備註：</span>
                      <p className="text-sm text-gray-800 mt-1">{shippingInfo.notes}</p>
                    </div>
                  )}

                  {/* 物流跟踪按钮 */}
                  <div className="mt-4">
                    <button
                      onClick={() => {
                        // 这里可以集成实际的物流跟踪API
                        toast.info('物流跟踪功能開發中...');
                      }}
                      className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <TruckIcon className="w-4 h-4 mr-2" />
                      跟踪物流
                    </button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-600">加載發貨信息中...</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* 右側：订单摘要和操作 */}
        <div className="lg:col-span-1">
          <div className="sticky top-4 space-y-6">
            {/* 價格摘要 */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">價格明細</h2>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">商品小計</span>
                  <span>¥{order.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">運費</span>
                  <span>{order.shippingFee === 0 ? '免費' : `¥${order.shippingFee.toFixed(2)}`}</span>
                </div>
                {order.discount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>優惠折扣</span>
                    <span>-¥{order.discount.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between text-lg font-semibold text-red-600 border-t border-gray-200 pt-3">
                  <span>總計</span>
                  <span>¥{order.totalAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>

            {/* 操作按鈕 */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">订单操作</h2>
              <div className="space-y-3">
                {order.status === OrderStatus.PENDING && (
                  <>
                    <button
                      onClick={handlePayment}
                      className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors"
                    >
                      立即付款
                    </button>
                    <button
                      onClick={handleCancelOrder}
                      className="w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors"
                    >
                      取消订单
                    </button>
                  </>
                )}

                {order.status === OrderStatus.DELIVERED && (
                  <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors">
                    確認收貨
                  </button>
                )}

                <Link
                  href="/orders"
                  className="block w-full text-center bg-gray-100 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  查看所有订单
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
