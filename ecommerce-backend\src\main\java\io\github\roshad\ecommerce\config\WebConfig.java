package io.github.roshad.ecommerce.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Autowired
    private RequestLoggingInterceptor requestLoggingInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(requestLoggingInterceptor)
                .addPathPatterns("/api/**") // 只拦截API请求
                .excludePathPatterns(
                    "/api/auth/login",  // 可以选择排除登录请求以避免记录密码
                    "/swagger-ui/**",   // 排除Swagger UI
                    "/v3/api-docs/**"   // 排除API文档
                );
    }
}
